//
//  PlaylistRouterView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON><PERSON> B on 23/04/25.
//


import SwiftUI
import Foundation
import GoogleSignIn
import SwiftData

// MARK: - Enum to handle all Navigation Routes
enum NavigationRoute: Hashable {
    case playlistDetailView
    case videoUploadView(videoUrl: URL?)
    case videoAnalyticsView(video: YouTubeVideo)
    case playlistVideoAnalyticsView(video: YouTubeVideo)
}

// MARK: - Coordinator for navigation logic
class NavigationCoordinator: ObservableObject {
    @Published var path = NavigationPath()

    func navigateToPlaylistDetailView() {
        path.append(NavigationRoute.playlistDetailView)
    }

    func goBackOneStep() {
        if !path.isEmpty {
            path.removeLast()
        }
    }

    func navigateToViewAnalytics(video: YouTubeVideo) {
        path.append(NavigationRoute.videoAnalyticsView(video: video))
    }

    func navigateToPlaylistVideoAnalyticsView(video: YouTubeVideo) {
        path.append(NavigationRoute.playlistVideoAnalyticsView(video: video))
    }
    
    func navigateToVideoUploadPage(with url:URL? = nil){
        path.append(NavigationRoute.videoUploadView(videoUrl: url))
    }
    
    func navigateToNewTab() {
        path.removeLast(path.count)
    }
//    func navigateToUploadVideoView(){
//        path.append(NavigationRoute.videosUploadView)
//    }
}

// MARK: - PlaylistRouterView as View
struct RootView: View {
    @StateObject private var navigationCoordinator = NavigationCoordinator()
    @EnvironmentObject var sharedVideoHandler: SharedVideoHandler
    @EnvironmentObject var googleSignInHelper: GoogleSignInHelper
    @EnvironmentObject var draftManager: DraftManager
    @Environment(\.modelContext) private var modelContext

    var isSignedIn: Bool {
        return googleSignInHelper.user != nil
    }

    var body: some View {
        Group {
            if let user = googleSignInHelper.user {
                DashboardView(signedInUser: user)

            } else {
                SignInView()
            }
        }
        .environmentObject(googleSignInHelper)
        .environmentObject(navigationCoordinator)
        .onAppear {
            // Initialize draft manager with model context
            draftManager.setModelContext(modelContext)
        }
    }
}
