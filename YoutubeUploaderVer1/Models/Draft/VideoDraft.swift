//
//  VideoDraft.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON> on 12/06/25.
//

import Foundation
import SwiftData

@Model
class VideoDraft {
    var id: UUID
    var createdAt: Date
    var updatedAt: Date
    var name: String
    
    // Video File Information
    var videoFileName: String?
    var videoFileSize: Int64?
    var videoDuration: Double?
    var videoFilePath: String? // Store relative path or bookmark data
    
    // Video Details
    var title: String
    var videoDescription: String
    var categoryId: String
    var privacyStatus: String
    var madeForKids: Bool
    var notifySubscribers: Bool
    
    // Audio and Transcript Data
    var audioFilePath: String?
    var transcriptText: String?
    var transcriptItems: Data? // Encoded [(TimeInterval, TimeInterval, String)]
    
    // AI Enhancement Data
    var aiEnhancements: [AIEnhancementDraft]
    
    // Progress Tracking
    var hasVideoFile: Bool
    var hasAudioExtracted: Bool
    var hasTranscript: Bool
    var hasTitle: Bool
    var hasDescription: Bool
    var completionPercentage: Double
    
    init(
        name: String = "Untitled Draft",
        title: String = "",
        videoDescription: String = "",
        categoryId: String = "22",
        privacyStatus: String = "Public",
        madeForKids: Bool = false,
        notifySubscribers: Bool = false
    ) {
        self.id = UUID()
        self.createdAt = Date()
        self.updatedAt = Date()
        self.name = name
        self.title = title
        self.videoDescription = videoDescription
        self.categoryId = categoryId
        self.privacyStatus = privacyStatus
        self.madeForKids = madeForKids
        self.notifySubscribers = notifySubscribers
        self.aiEnhancements = []
        self.hasVideoFile = false
        self.hasAudioExtracted = false
        self.hasTranscript = false
        self.hasTitle = false
        self.hasDescription = false
        self.completionPercentage = 0.0
    }
    
    // MARK: - Computed Properties
    
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: createdAt)
    }
    
    var formattedUpdatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: updatedAt)
    }
    
    var timeAgo: String {
        let now = Date()
        let timeInterval = now.timeIntervalSince(updatedAt)
        
        if timeInterval < 60 {
            return "Just now"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes) minute\(minutes == 1 ? "" : "s") ago"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours) hour\(hours == 1 ? "" : "s") ago"
        } else {
            let days = Int(timeInterval / 86400)
            return "\(days) day\(days == 1 ? "" : "s") ago"
        }
    }
    
    var statusSummary: String {
        var components: [String] = []
        
        if hasVideoFile { components.append("Video") }
        if hasTranscript { components.append("Transcript") }
        if hasTitle { components.append("Title") }
        if hasDescription { components.append("Description") }
        if !aiEnhancements.isEmpty { components.append("AI Enhanced") }
        
        return components.isEmpty ? "Empty draft" : components.joined(separator: " • ")
    }
    
    // MARK: - Helper Methods
    
    func updateProgress() {
        var progress: Double = 0.0
        let totalSteps: Double = 5.0 // Video, Audio, Transcript, Title, Description
        
        if hasVideoFile { progress += 1.0 }
        if hasAudioExtracted { progress += 1.0 }
        if hasTranscript { progress += 1.0 }
        if hasTitle { progress += 1.0 }
        if hasDescription { progress += 1.0 }
        
        completionPercentage = (progress / totalSteps) * 100.0
        updatedAt = Date()
    }
    
    func updateFromVideoUploadViewModel(_ viewModel: VideoUploadViewModel) {
        title = viewModel.title
        videoDescription = viewModel.description
        categoryId = viewModel.categoryId
        privacyStatus = viewModel.privacyStatus
        madeForKids = viewModel.madeForKids
        notifySubscribers = viewModel.notifySubscribers
        
        hasTitle = !title.isEmpty
        hasDescription = !videoDescription.isEmpty
        
        updateProgress()
    }
    
    func applyToVideoUploadViewModel(_ viewModel: VideoUploadViewModel) {
        viewModel.title = title
        viewModel.description = videoDescription
        viewModel.categoryId = categoryId
        viewModel.privacyStatus = privacyStatus
        viewModel.madeForKids = madeForKids
        viewModel.notifySubscribers = notifySubscribers
    }
    
    func setVideoFile(url: URL) {
        videoFileName = url.lastPathComponent
        videoFilePath = url.path
        
        // Get file size
        do {
            let attributes = try FileManager.default.attributesOfItem(atPath: url.path)
            videoFileSize = attributes[.size] as? Int64 ?? 0
        } catch {
            print("Failed to get file size: \(error)")
        }
        
        hasVideoFile = true
        
        // Auto-generate name if it's still "Untitled Draft"
        if name == "Untitled Draft" {
            name = url.deletingPathExtension().lastPathComponent
        }
        
        updateProgress()
    }
    
    func setTranscript(items: [(TimeInterval, TimeInterval, String)]) {
        // Encode transcript items
        do {
            transcriptItems = try JSONEncoder().encode(items.map { TranscriptItem(start: $0.0, end: $0.1, text: $0.2) })
            transcriptText = items.map { $0.2 }.joined(separator: " ")
            hasTranscript = true
            updateProgress()
        } catch {
            print("Failed to encode transcript items: \(error)")
        }
    }
    
    func getTranscriptItems() -> [(TimeInterval, TimeInterval, String)] {
        guard let transcriptItems = transcriptItems else { return [] }
        
        do {
            let items = try JSONDecoder().decode([TranscriptItem].self, from: transcriptItems)
            return items.map { ($0.start, $0.end, $0.text) }
        } catch {
            print("Failed to decode transcript items: \(error)")
            return []
        }
    }
}

// MARK: - Supporting Models

struct TranscriptItem: Codable {
    let start: TimeInterval
    let end: TimeInterval
    let text: String
}

@Model
class AIEnhancementDraft {
    var id: UUID
    var type: String // "videoSummarization", "contentFreshness", etc.
    var content: String
    var createdAt: Date
    var videoDraft: VideoDraft?
    
    init(type: String, content: String) {
        self.id = UUID()
        self.type = type
        self.content = content
        self.createdAt = Date()
    }
}
