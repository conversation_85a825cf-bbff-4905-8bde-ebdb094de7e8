//
//  AIDetailView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON>  on 26/05/25.
//

import SwiftUI

struct AIDetailView: View {
    let option: AIOption
    let transcriptItems: [(TimeInterval, TimeInterval, String)]
    let selectedFileURL: URL?
    let videoTitle: String
    let videoDescription: String
    let videoCategory: String
    @State private var showCopyAlert = false
    @StateObject private var viewModel = VideoSummaryViewModel()

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {

            // Content Area with proper spacing
            modernContentArea
        }
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color.opacity(0.95))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppColor.accentBlue.color.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: AppColor.accentBlue.color.opacity(0.1), radius: 12, x: 0, y: 4)
        .transition(.asymmetric(
            insertion: .opacity.combined(with: .move(edge: .top)).combined(with: .scale(scale: 0.95)),
            removal: .opacity.combined(with: .move(edge: .top))
        ))
    }


    // MARK: - Content Area
    private var modernContentArea: some View {
        VStack(alignment: .leading, spacing: 20) {
            switch option {
            case .videoSummarization:
                VideoSummarizationView(
                    viewModel: viewModel,
                    transcriptItems: transcriptItems
                )

            case .contentFreshness:
                ContentFreshnessView(
                    title: videoTitle,
                    description: videoDescription,
                    transcript: transcriptItems,
                    tags: [],
                    category: videoCategory
                )

            case .performancePredictor:
                PerformancePredictorView(
                    title: videoTitle,
                    description: videoDescription,
                    category: videoCategory,
                    thumbnailDescription: ""
                )

            case .shortsClips, .contentRecreation:
               ShortsClipsCreationView(
                    videoURL: selectedFileURL,
                    viewModel: viewModel,
                    transcriptItems: transcriptItems,
                )
            }
        }
        .padding(20)
    }

    // MARK: - Computed Properties
    private var isProcessing: Bool {
        switch option {
        case .videoSummarization:
            return viewModel.isLoading
        case .contentFreshness:
            return false // Will be updated when we implement the modern version
        case .performancePredictor:
            return false // Will be updated when we implement the modern version
        case .shortsClips, .contentRecreation:
            return viewModel.isLoading
        }
    }
}




