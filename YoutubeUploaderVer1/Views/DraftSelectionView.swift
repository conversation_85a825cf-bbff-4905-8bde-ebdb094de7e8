//
//  DraftSelectionView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON> on 12/06/25.
//

import SwiftUI

struct DraftSelectionView: View {
    @EnvironmentObject private var draftManager: DraftManager
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var showingDeleteAlert = false
    @State private var draftToDelete: VideoDraft?
    
    let onDraftSelected: (VideoDraft) -> Void
    
    var filteredDrafts: [VideoDraft] {
        draftManager.searchDrafts(query: searchText)
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            modernHeader
            
            // Search Bar
            modernSearchBar
            
            // Drafts List
            if filteredDrafts.isEmpty {
                emptyStateView
            } else {
                modernDraftsList
            }
        }
        .frame(width: 600, height: 500)
        .background(AppColor.surfacePrimary.color)
        .alert("Delete Draft", isPresented: $showingDeleteAlert) {
            But<PERSON>("Cancel", role: .cancel) { }
            But<PERSON>("Delete", role: .destructive) {
                if let draft = draftToDelete {
                    draftManager.deleteDraft(draft)
                }
            }
        } message: {
            Text("Are you sure you want to delete this draft? This action cannot be undone.")
        }
    }
    
    // MARK: - Header
    private var modernHeader: some View {
        VStack(spacing: 0) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Select Draft")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text("Choose a draft to continue working on")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
                
                Button(action: { dismiss() }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(AppColor.textTertiary.color)
                }
                .buttonStyle(.plain)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            
            Divider()
                .background(AppColor.borderPrimary.color.opacity(0.3))
        }
    }
    
    // MARK: - Search Bar
    private var modernSearchBar: some View {
        HStack(spacing: 12) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(AppColor.textTertiary.color)
            
            TextField("Search drafts...", text: $searchText)
                .textFieldStyle(.plain)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppColor.textPrimary.color)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(AppColor.surfaceSecondary.color.opacity(0.5))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                )
        )
        .padding(.horizontal, 24)
        .padding(.vertical, 16)
    }
    
    // MARK: - Drafts List
    private var modernDraftsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(filteredDrafts) { draft in
                    modernDraftCard(draft)
                }
            }
            .padding(.horizontal, 24)
            .padding(.bottom, 24)
        }
    }
    
    // MARK: - Draft Card
    private func modernDraftCard(_ draft: VideoDraft) -> some View {
        Button(action: {
            onDraftSelected(draft)
            dismiss()
        }) {
            VStack(alignment: .leading, spacing: 12) {
                // Header Row
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(draft.name)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(AppColor.textPrimary.color)
                            .lineLimit(1)
                        
                        Text(draft.timeAgo)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(AppColor.textTertiary.color)
                    }
                    
                    Spacer()
                    
                    // Actions
                    HStack(spacing: 8) {
                        Button(action: {
                            let _ = draftManager.duplicateDraft(draft)
                        }) {
                            Image(systemName: "doc.on.doc")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(AppColor.textTertiary.color)
                        }
                        .buttonStyle(.plain)
                        .help("Duplicate draft")
                        
                        Button(action: {
                            draftToDelete = draft
                            showingDeleteAlert = true
                        }) {
                            Image(systemName: "trash")
                                .font(.system(size: 14, weight: .medium))
                                .foregroundColor(.red)
                        }
                        .buttonStyle(.plain)
                        .help("Delete draft")
                    }
                }
                
                // Content Preview
                if !draft.title.isEmpty {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Title")
                            .font(.system(size: 11, weight: .semibold))
                            .foregroundColor(AppColor.textTertiary.color)
                            .textCase(.uppercase)
                        
                        Text(draft.title)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(AppColor.textSecondary.color)
                            .lineLimit(2)
                    }
                }
                
                // Status and Progress
                HStack {
                    Text(draft.statusSummary)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                    
                    Spacer()
                    
                    // Progress Bar
                    HStack(spacing: 8) {
                        ProgressView(value: draft.completionPercentage, total: 100)
                            .progressViewStyle(LinearProgressViewStyle())
                            .frame(width: 60)
                        
                        Text("\(Int(draft.completionPercentage))%")
                            .font(.system(size: 11, weight: .semibold))
                            .foregroundColor(AppColor.textTertiary.color)
                    }
                }
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(AppColor.surfaceSecondary.color.opacity(0.5))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(.plain)
        .onHover { isHovered in
            // Add hover effect if needed
        }
    }
    
    // MARK: - Empty State
    private var emptyStateView: some View {
        VStack(spacing: 16) {
            Image(systemName: "doc.text")
                .font(.system(size: 48, weight: .light))
                .foregroundColor(AppColor.textTertiary.color)
            
            VStack(spacing: 8) {
                Text(searchText.isEmpty ? "No Drafts Yet" : "No Matching Drafts")
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                Text(searchText.isEmpty ? 
                     "Start uploading a video to create your first draft" :
                     "Try adjusting your search terms")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(AppColor.surfacePrimary.color)
    }
}

#Preview {
    DraftSelectionView { _ in }
        .environmentObject(DraftManager.shared)
}
