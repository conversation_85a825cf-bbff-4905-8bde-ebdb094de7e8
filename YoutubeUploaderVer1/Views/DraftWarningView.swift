//
//  DraftWarningView.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON> on 12/06/25.
//

import SwiftUI

struct DraftWarningView: View {
    @EnvironmentObject private var draftManager: DraftManager
    @Environment(\.dismiss) private var dismiss
    @State private var draftName: String = ""
    @State private var showingNameInput = false
    
    let onSaveAndProceed: () -> Void
    let onDiscardAndProceed: () -> Void
    let onCancel: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            modernHeader
            
            // Content
            modernContent
            
            // Actions
            modernActions
        }
        .frame(width: 480, height: 320)
        .background(AppColor.surfacePrimary.color)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
        )
        .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
        .onAppear {
            draftName = draftManager.currentDraft?.name ?? draftManager.generateDraftName()
        }
    }
    
    // MARK: - Header
    private var modernHeader: some View {
        VStack(spacing: 0) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.orange)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Unsaved Changes")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(AppColor.textPrimary.color)
                    
                    Text("You have unsaved progress that will be lost")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                
                Spacer()
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
            
            Divider()
                .background(AppColor.borderPrimary.color.opacity(0.3))
        }
    }
    
    // MARK: - Content
    private var modernContent: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Current Progress Summary
            if let draft = draftManager.currentDraft {
                modernProgressSummary(draft)
            }
            
            // Options Description
            VStack(alignment: .leading, spacing: 12) {
                Text("What would you like to do?")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                VStack(alignment: .leading, spacing: 8) {
                    optionDescription(
                        icon: "square.and.arrow.down",
                        title: "Save as Draft",
                        description: "Keep your progress and continue later",
                        color: AppColor.accentBlue.color
                    )
                    
                    optionDescription(
                        icon: "trash",
                        title: "Discard Changes",
                        description: "Lose all progress and start fresh",
                        color: .red
                    )
                    
                    optionDescription(
                        icon: "xmark.circle",
                        title: "Cancel",
                        description: "Stay on this page and continue working",
                        color: AppColor.textTertiary.color
                    )
                }
            }
        }
        .padding(.horizontal, 24)
        .padding(.vertical, 16)
    }
    
    // MARK: - Progress Summary
    private func modernProgressSummary(_ draft: VideoDraft) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Current Progress")
                .font(.system(size: 14, weight: .semibold))
                .foregroundColor(AppColor.textPrimary.color)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    ProgressView(value: draft.completionPercentage, total: 100)
                        .progressViewStyle(LinearProgressViewStyle())
                    
                    Text("\(Int(draft.completionPercentage))%")
                        .font(.system(size: 12, weight: .semibold))
                        .foregroundColor(AppColor.textTertiary.color)
                }
                
                Text(draft.statusSummary)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(AppColor.surfaceSecondary.color.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Option Description
    private func optionDescription(icon: String, title: String, description: String, color: Color) -> some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(color)
                .frame(width: 20)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.system(size: 13, weight: .semibold))
                    .foregroundColor(AppColor.textPrimary.color)
                
                Text(description)
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Actions
    private var modernActions: some View {
        VStack(spacing: 0) {
            Divider()
                .background(AppColor.borderPrimary.color.opacity(0.3))
            
            HStack(spacing: 12) {
                // Cancel Button
                Button(action: {
                    onCancel()
                    dismiss()
                }) {
                    Text("Cancel")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(AppColor.textSecondary.color)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(AppColor.surfaceSecondary.color.opacity(0.5))
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                                )
                        )
                }
                .buttonStyle(.plain)
                
                // Discard Button
                Button(action: {
                    onDiscardAndProceed()
                    dismiss()
                }) {
                    Text("Discard")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(.red)
                        )
                }
                .buttonStyle(.plain)
                
                // Save Button
                Button(action: {
                    saveDraftAndProceed()
                }) {
                    Text("Save Draft")
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(AppColor.accentBlue.color)
                        )
                }
                .buttonStyle(.plain)
            }
            .padding(.horizontal, 24)
            .padding(.vertical, 20)
        }
    }
    
    // MARK: - Actions
    
    private func saveDraftAndProceed() {
        if let currentDraft = draftManager.currentDraft {
            // Update the draft name if it's been changed
            if !draftName.isEmpty && draftName != currentDraft.name {
                currentDraft.name = draftName
            }
            draftManager.saveDraft(currentDraft)
        }
        
        onSaveAndProceed()
        dismiss()
    }
}

// MARK: - Extension for DraftManager

extension DraftManager {
    func generateDraftName() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM dd, yyyy 'at' h:mm a"
        return "Draft - \(formatter.string(from: Date()))"
    }
}

#Preview {
    DraftWarningView(
        onSaveAndProceed: {},
        onDiscardAndProceed: {},
        onCancel: {}
    )
    .environmentObject(DraftManager.shared)
}
