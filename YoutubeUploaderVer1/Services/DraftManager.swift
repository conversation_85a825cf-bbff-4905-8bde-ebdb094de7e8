//
//  DraftManager.swift
//  YoutubeUploaderVer1
//
//  Created by <PERSON><PERSON><PERSON> on 12/06/25.
//

import Foundation
import SwiftData
import SwiftUI

@MainActor
class DraftManager: ObservableObject {
    static let shared = DraftManager()
    
    @Published var currentDraft: VideoDraft?
    @Published var drafts: [VideoDraft] = []
    @Published var isAutoSaveEnabled: Bool = true
    @Published var lastAutoSaveTime: Date?
    
    private var modelContext: ModelContext?
    private var autoSaveTimer: Timer?
    private let autoSaveInterval: TimeInterval = 30.0 // Auto-save every 30 seconds
    
    private init() {
        setupAutoSave()
    }
    
    // MARK: - Setup
    
    func setModelContext(_ context: ModelContext) {
        self.modelContext = context
        loadDrafts()
    }
    
    private func setupAutoSave() {
        autoSaveTimer = Timer.scheduledTimer(withTimeInterval: autoSaveInterval, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.autoSave()
            }
        }
    }
    
    // MARK: - Draft Management
    
    func createNewDraft(name: String? = nil) -> VideoDraft {
        let draft = VideoDraft(name: name ?? generateDraftName())
        currentDraft = draft
        return draft
    }
    
    func saveDraft(_ draft: VideoDraft) {
        guard let context = modelContext else {
            print("❌ ModelContext not available")
            return
        }
        
        draft.updatedAt = Date()
        context.insert(draft)
        
        do {
            try context.save()
            loadDrafts()
            print("✅ Draft saved: \(draft.name)")
        } catch {
            print("❌ Failed to save draft: \(error)")
        }
    }
    
    func updateCurrentDraft(from viewModel: VideoUploadViewModel) {
        guard let draft = currentDraft else { return }
        
        draft.updateFromVideoUploadViewModel(viewModel)
        
        if let context = modelContext {
            do {
                try context.save()
                print("✅ Current draft updated")
            } catch {
                print("❌ Failed to update current draft: \(error)")
            }
        }
    }
    
    func loadDraft(_ draft: VideoDraft) -> VideoDraft {
        currentDraft = draft
        return draft
    }
    
    func deleteDraft(_ draft: VideoDraft) {
        guard let context = modelContext else { return }
        
        context.delete(draft)
        
        do {
            try context.save()
            loadDrafts()
            
            // If we deleted the current draft, clear it
            if currentDraft?.id == draft.id {
                currentDraft = nil
            }
            
            print("✅ Draft deleted: \(draft.name)")
        } catch {
            print("❌ Failed to delete draft: \(error)")
        }
    }
    
    func duplicateDraft(_ draft: VideoDraft) -> VideoDraft {
        let newDraft = VideoDraft(
            name: "\(draft.name) Copy",
            title: draft.title,
            videoDescription: draft.videoDescription,
            categoryId: draft.categoryId,
            privacyStatus: draft.privacyStatus,
            madeForKids: draft.madeForKids,
            notifySubscribers: draft.notifySubscribers
        )
        
        // Copy other properties
        newDraft.videoFileName = draft.videoFileName
        newDraft.videoFileSize = draft.videoFileSize
        newDraft.videoDuration = draft.videoDuration
        newDraft.videoFilePath = draft.videoFilePath
        newDraft.audioFilePath = draft.audioFilePath
        newDraft.transcriptText = draft.transcriptText
        newDraft.transcriptItems = draft.transcriptItems
        newDraft.hasVideoFile = draft.hasVideoFile
        newDraft.hasAudioExtracted = draft.hasAudioExtracted
        newDraft.hasTranscript = draft.hasTranscript
        newDraft.hasTitle = draft.hasTitle
        newDraft.hasDescription = draft.hasDescription
        newDraft.completionPercentage = draft.completionPercentage
        
        saveDraft(newDraft)
        return newDraft
    }
    
    private func loadDrafts() {
        guard let context = modelContext else { return }
        
        let descriptor = FetchDescriptor<VideoDraft>(
            sortBy: [SortDescriptor(\.updatedAt, order: .reverse)]
        )
        
        do {
            drafts = try context.fetch(descriptor)
            print("✅ Loaded \(drafts.count) drafts")
        } catch {
            print("❌ Failed to load drafts: \(error)")
            drafts = []
        }
    }
    
    // MARK: - Auto Save
    
    private func autoSave() async {
        guard isAutoSaveEnabled,
              let draft = currentDraft,
              let context = modelContext else { return }
        
        do {
            try context.save()
            lastAutoSaveTime = Date()
            print("🔄 Auto-saved draft: \(draft.name)")
        } catch {
            print("❌ Auto-save failed: \(error)")
        }
    }
    
    func enableAutoSave() {
        isAutoSaveEnabled = true
    }
    
    func disableAutoSave() {
        isAutoSaveEnabled = false
    }
    
    // MARK: - Utility
    
    private func generateDraftName() -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM dd, yyyy 'at' h:mm a"
        return "Draft - \(formatter.string(from: Date()))"
    }
    
    func hasUnsavedChanges(for viewModel: VideoUploadViewModel) -> Bool {
        guard let draft = currentDraft else { return false }
        
        return draft.title != viewModel.title ||
               draft.videoDescription != viewModel.description ||
               draft.categoryId != viewModel.categoryId ||
               draft.privacyStatus != viewModel.privacyStatus ||
               draft.madeForKids != viewModel.madeForKids ||
               draft.notifySubscribers != viewModel.notifySubscribers
    }
    
    func getRecentDrafts(limit: Int = 5) -> [VideoDraft] {
        return Array(drafts.prefix(limit))
    }
    
    func searchDrafts(query: String) -> [VideoDraft] {
        if query.isEmpty {
            return drafts
        }
        
        return drafts.filter { draft in
            draft.name.localizedCaseInsensitiveContains(query) ||
            draft.title.localizedCaseInsensitiveContains(query) ||
            draft.videoDescription.localizedCaseInsensitiveContains(query)
        }
    }
    
    // MARK: - Video File Management
    
    func setVideoFile(url: URL, for draft: VideoDraft? = nil) {
        let targetDraft = draft ?? currentDraft
        guard let targetDraft = targetDraft else { return }
        
        targetDraft.setVideoFile(url: url)
        
        if let context = modelContext {
            do {
                try context.save()
            } catch {
                print("❌ Failed to save video file info: \(error)")
            }
        }
    }
    
    func setTranscript(items: [(TimeInterval, TimeInterval, String)], for draft: VideoDraft? = nil) {
        let targetDraft = draft ?? currentDraft
        guard let targetDraft = targetDraft else { return }
        
        targetDraft.setTranscript(items: items)
        
        if let context = modelContext {
            do {
                try context.save()
            } catch {
                print("❌ Failed to save transcript: \(error)")
            }
        }
    }
    
    func addAIEnhancement(type: String, content: String, for draft: VideoDraft? = nil) {
        let targetDraft = draft ?? currentDraft
        guard let targetDraft = targetDraft else { return }
        
        let enhancement = AIEnhancementDraft(type: type, content: content)
        enhancement.videoDraft = targetDraft
        targetDraft.aiEnhancements.append(enhancement)
        targetDraft.updatedAt = Date()
        
        if let context = modelContext {
            do {
                try context.save()
            } catch {
                print("❌ Failed to save AI enhancement: \(error)")
            }
        }
    }
    
    deinit {
        autoSaveTimer?.invalidate()
    }
}
