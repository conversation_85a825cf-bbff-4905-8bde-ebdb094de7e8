//
//  ScriptWriterView.swift
//  YoutubeUploaderVer1
//
//  Created by AI Assistant on 05/01/25.
//

import SwiftUI
import MarkdownUI

struct ScriptWriterView: View {
    @StateObject private var scriptManager = ScriptWriterManager()
    
    let localAIService = LocalAIService.shared
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // Modern Header
                modernHeaderSection

                // Script Parameters
                if !scriptManager.isGenerating {
                    modernScriptParametersSection
                }

                // Generation Progress
                if scriptManager.isGenerating {
                    modernGenerationProgressSection
                }

                // Generated Script Display
                if !scriptManager.generatedScript.isEmpty && !scriptManager.isGenerating {
                    modernGeneratedScriptSection
                }

                // Generate Button
                modernGenerateButtonSection
            }
            .padding(20)
        }
        .background(AppColor.darkBackground.color)
        .alert("Script Generation Error", isPresented: $scriptManager.showErrorAlert) {
            Button("OK") {
                scriptManager.showErrorAlert = false
            }
        } message: {
            Text(scriptManager.errorMessage ?? "An unknown error occurred")
        }
        .onAppear{
            Task{
                await localAIService.initializeModel()
            }
        }
    }
    

    // MARK: - Modern Header Section
    private var modernHeaderSection: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack(alignment: .top) {
                // Title and Subtitle with Gradient
                VStack(alignment: .leading, spacing: 8) {
                    Text("AI Script Writer")
                        .font(.system(size: 32, weight: .bold, design: .rounded))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [AppColor.textPrimary.color, AppColor.accentBlue.color],
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )

                    Text("Generate professional video scripts with AI-powered intelligence")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }

                Spacer()


            }

        }
        .padding(.horizontal, 24)
        .padding(.top, 16)
        .padding(.bottom, 24)
    }





    private func featureHighlightCard(icon: String, title: String, subtitle: String, color: Color) -> some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 32, height: 32)
                .background(
                    Circle()
                        .fill(color.opacity(0.1))
                )

            VStack(spacing: 2) {
                Text(title)
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text(subtitle)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(AppColor.textSecondary.color)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }

    // MARK: - Modern Script Parameters Section
    private var modernScriptParametersSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            // Section Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Script Configuration")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(AppColor.textPrimary.color)

                    Text("Customize your script parameters for optimal results")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }

                Spacer()

                // Configuration Status Badge
                configurationStatusBadge
            }

            VStack(alignment: .leading, spacing: 20) {
                // Topic Input Section
                modernTopicInputSection

                // Script Configuration Grid
                modernConfigurationGrid

                // Advanced Options
                modernAdvancedOptionsSection
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
    }

    // MARK: - Configuration Status Badge
    private var configurationStatusBadge: some View {
        HStack(spacing: 6) {
            Image(systemName: isConfigurationComplete ? "checkmark.circle.fill" : "exclamationmark.circle.fill")
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(isConfigurationComplete ? Color.green : Color.orange)

            Text(isConfigurationComplete ? "Ready to Generate" : "Configuration Needed")
                .font(.system(size: 11, weight: .semibold))
                .foregroundColor(isConfigurationComplete ? Color.green : Color.orange)
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill((isConfigurationComplete ? Color.green : Color.orange).opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke((isConfigurationComplete ? Color.green : Color.orange).opacity(0.3), lineWidth: 1)
                )
        )
    }

    private var isConfigurationComplete: Bool {
        !scriptManager.topic.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - Modern Topic Input Section
    private var modernTopicInputSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Video Topic")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Text("*")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppColor.youtubeRed.color)

                Spacer()

                // Character count
                Text("\(scriptManager.topic.count)/500")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
            }

            TextField("Describe your video topic or idea in detail...", text: $scriptManager.topic, axis: .vertical)
                .textFieldStyle(.plain)
                .padding(16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(AppColor.surfaceSecondary.color)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    scriptManager.topic.isEmpty ? AppColor.borderPrimary.color.opacity(0.3) : AppColor.accentBlue.color.opacity(0.5),
                                    lineWidth: scriptManager.topic.isEmpty ? 1 : 2
                                )
                        )
                )
                .lineLimit(3...8)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(AppColor.textPrimary.color)

            Text("💡 Be specific about your topic, target audience, and key points you want to cover")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(AppColor.textTertiary.color)
                .padding(.horizontal, 4)
        }
    }

    // MARK: - Modern Configuration Grid
    private var modernConfigurationGrid: some View {
        VStack(alignment: .leading, spacing: 20) {
            HStack {
                Text("Script Settings")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()

                // Quick Settings Badge
                HStack(spacing: 4) {
                    Image(systemName: "slider.horizontal.3")
                        .font(.system(size: 10, weight: .medium))
                    Text("Customizable")
                        .font(.system(size: 10, weight: .semibold))
                }
                .foregroundColor(AppColor.accentBlue.color)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    RoundedRectangle(cornerRadius: 6)
                        .fill(AppColor.accentBlue.color.opacity(0.1))
                )
            }

            // Configuration Overview Cards
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                modernConfigOverviewCard(
                    title: "Video Type",
                    value: scriptManager.selectedType.rawValue,
                    icon: scriptManager.selectedType.icon,
                    color: AppColor.accentBlue.color,
                    description: "Content category"
                )

                modernConfigOverviewCard(
                    title: "Duration",
                    value: scriptManager.selectedLength.rawValue,
                    icon: "clock.fill",
                    color: Color.green,
                    description: scriptManager.selectedLength.estimatedWords
                )

                modernConfigOverviewCard(
                    title: "Tone & Style",
                    value: scriptManager.selectedTone.rawValue,
                    icon: "speaker.wave.2.fill",
                    color: Color.purple,
                    description: scriptManager.selectedTone.description
                )
            }

            // Detailed Selectors
            VStack(spacing: 16) {
                modernScriptTypeSelector
                modernScriptLengthSelector
                modernScriptToneSelector
            }
        }
    }

    private func modernConfigOverviewCard(title: String, value: String, icon: String, color: Color, description: String) -> some View {
        VStack(spacing: 12) {
            // Icon with background
            Image(systemName: icon)
                .font(.system(size: 20, weight: .semibold))
                .foregroundColor(color)
                .frame(width: 40, height: 40)
                .background(
                    RoundedRectangle(cornerRadius: 10)
                        .fill(color.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 10)
                                .stroke(color.opacity(0.2), lineWidth: 1)
                        )
                )

            VStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)
                    .textCase(.uppercase)
                    .tracking(0.5)

                Text(value)
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)
                    .lineLimit(1)

                Text(description)
                    .font(.system(size: 10, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
                    .lineLimit(2)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .padding(.horizontal, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(AppColor.surfacePrimary.color.opacity(0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(AppColor.borderPrimary.color.opacity(0.2), lineWidth: 1)
                )
        )
        .shadow(color: Color.black.opacity(0.05), radius: 4, x: 0, y: 2)
    }

    // MARK: - Modern Selectors
    private var modernScriptTypeSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Select Video Type")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()

                Text("\(ScriptType.allCases.count) Options")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ScriptType.allCases, id: \.self) { type in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            scriptManager.selectedType = type
                        }
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type.icon)
                                .font(.system(size: 18, weight: .semibold))
                                .foregroundColor(scriptManager.selectedType == type ? .white : AppColor.accentBlue.color)
                                .frame(width: 32, height: 32)
                                .background(
                                    Circle()
                                        .fill(scriptManager.selectedType == type ? AppColor.accentBlue.color : AppColor.accentBlue.color.opacity(0.1))
                                )

                            Text(type.rawValue)
                                .font(.system(size: 11, weight: .bold))
                                .foregroundColor(scriptManager.selectedType == type ? AppColor.textPrimary.color : AppColor.textSecondary.color)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 8)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(scriptManager.selectedType == type ? AppColor.surfacePrimary.color : AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            scriptManager.selectedType == type ? AppColor.accentBlue.color : AppColor.borderPrimary.color.opacity(0.3),
                                            lineWidth: scriptManager.selectedType == type ? 2 : 1
                                        )
                                )
                        )
                        .shadow(color: scriptManager.selectedType == type ? AppColor.accentBlue.color.opacity(0.2) : Color.clear, radius: 4, x: 0, y: 2)
                    }
                    .buttonStyle(.plain)
                }
            }

            if scriptManager.selectedType != .educational {
                Text(scriptManager.selectedType.description)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
                    .padding(.horizontal, 4)
                    .padding(.top, 4)
            }
        }
    }

    private var modernScriptLengthSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Choose Duration")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()

                HStack(spacing: 4) {
                    Image(systemName: "clock.fill")
                        .font(.system(size: 10, weight: .medium))
                    Text("Estimated Words")
                        .font(.system(size: 11, weight: .medium))
                }
                .foregroundColor(AppColor.textTertiary.color)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ScriptLength.allCases, id: \.self) { length in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            scriptManager.selectedLength = length
                        }
                    }) {
                        VStack(spacing: 8) {
                            HStack(spacing: 8) {
                                Image(systemName: "clock.fill")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(scriptManager.selectedLength == length ? .white : Color.green)

                                VStack(alignment: .leading, spacing: 2) {
                                    Text(length.rawValue)
                                        .font(.system(size: 13, weight: .bold))
                                        .foregroundColor(scriptManager.selectedLength == length ? .white : AppColor.textPrimary.color)

                                    Text(length.estimatedWords)
                                        .font(.system(size: 11, weight: .medium))
                                        .foregroundColor(scriptManager.selectedLength == length ? .white.opacity(0.8) : AppColor.textSecondary.color)
                                }

                                Spacer()
                            }
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(scriptManager.selectedLength == length ? Color.green : AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            scriptManager.selectedLength == length ? Color.green : AppColor.borderPrimary.color.opacity(0.3),
                                            lineWidth: scriptManager.selectedLength == length ? 2 : 1
                                        )
                                )
                        )
                        .shadow(color: scriptManager.selectedLength == length ? Color.green.opacity(0.2) : Color.clear, radius: 4, x: 0, y: 2)
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }

    private var modernScriptToneSelector: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Select Tone & Style")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()

                HStack(spacing: 4) {
                    Image(systemName: "speaker.wave.2.fill")
                        .font(.system(size: 10, weight: .medium))
                    Text("Voice & Personality")
                        .font(.system(size: 11, weight: .medium))
                }
                .foregroundColor(AppColor.textTertiary.color)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(ScriptTone.allCases, id: \.self) { tone in
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            scriptManager.selectedTone = tone
                        }
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: "speaker.wave.2.fill")
                                .font(.system(size: 16, weight: .semibold))
                                .foregroundColor(scriptManager.selectedTone == tone ? .white : Color.purple)
                                .frame(width: 28, height: 28)
                                .background(
                                    Circle()
                                        .fill(scriptManager.selectedTone == tone ? Color.purple : Color.purple.opacity(0.1))
                                )

                            VStack(spacing: 4) {
                                Text(tone.rawValue)
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(scriptManager.selectedTone == tone ? AppColor.textPrimary.color : AppColor.textSecondary.color)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(1)

                                Text(tone.description)
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(scriptManager.selectedTone == tone ? AppColor.textSecondary.color : AppColor.textTertiary.color)
                                    .multilineTextAlignment(.center)
                                    .lineLimit(2)
                            }
                        }
                        .padding(.vertical, 12)
                        .padding(.horizontal, 8)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(scriptManager.selectedTone == tone ? AppColor.surfacePrimary.color : AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(
                                            scriptManager.selectedTone == tone ? Color.purple : AppColor.borderPrimary.color.opacity(0.3),
                                            lineWidth: scriptManager.selectedTone == tone ? 2 : 1
                                        )
                                )
                        )
                        .shadow(color: scriptManager.selectedTone == tone ? Color.purple.opacity(0.2) : Color.clear, radius: 4, x: 0, y: 2)
                    }
                    .buttonStyle(.plain)
                }
            }
        }
    }

    // MARK: - Modern Advanced Options
    private var modernAdvancedOptionsSection: some View {
        DisclosureGroup {
            VStack(alignment: .leading, spacing: 16) {
                // Target Audience
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Target Audience")
                            .font(.system(size: 13, weight: .bold))
                            .foregroundColor(AppColor.textPrimary.color)

                        Spacer()

                        Text("Optional")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(AppColor.textTertiary.color)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(AppColor.textTertiary.color.opacity(0.1))
                            )
                    }

                    TextField("e.g., Beginners, Tech enthusiasts, Young adults...", text: $scriptManager.targetAudience)
                        .textFieldStyle(.plain)
                        .padding(12)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 10)
                                        .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .font(.system(size: 13, weight: .medium))
                }

                // Key Points
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Key Points to Cover")
                            .font(.system(size: 13, weight: .bold))
                            .foregroundColor(AppColor.textPrimary.color)

                        Spacer()

                        Text("Optional")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(AppColor.textTertiary.color)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(AppColor.textTertiary.color.opacity(0.1))
                            )
                    }

                    TextField("List important points you want to include...", text: $scriptManager.keyPoints, axis: .vertical)
                        .textFieldStyle(.plain)
                        .padding(12)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(AppColor.surfaceSecondary.color)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 10)
                                        .stroke(AppColor.borderPrimary.color.opacity(0.3), lineWidth: 1)
                                )
                        )
                        .lineLimit(2...4)
                        .font(.system(size: 13, weight: .medium))
                }

                // Options
                VStack(alignment: .leading, spacing: 12) {
                    Text("Script Options")
                        .font(.system(size: 13, weight: .bold))
                        .foregroundColor(AppColor.textPrimary.color)

                    HStack {
                        Toggle("Include engaging hook", isOn: $scriptManager.includeHook)
                            .toggleStyle(SwitchToggleStyle(tint: AppColor.accentBlue.color))
                            .font(.system(size: 13, weight: .medium))
                            .foregroundColor(AppColor.textPrimary.color)

                        Spacer()

                        Text("Recommended")
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(Color.green)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(
                                RoundedRectangle(cornerRadius: 4)
                                    .fill(Color.green.opacity(0.1))
                            )
                    }
                }
            }
            .padding(.top, 12)
        } label: {
            HStack {
                Image(systemName: "gearshape.fill")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(AppColor.accentBlue.color)

                Text("Advanced Options")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()

                Text("Customize Further")
                    .font(.system(size: 11, weight: .medium))
                    .foregroundColor(AppColor.textTertiary.color)
            }
        }
        .accentColor(AppColor.accentBlue.color)
    }

    // MARK: - Modern Generation Progress Section
    private var modernGenerationProgressSection: some View {
        VStack(spacing: 12) {
            HStack(spacing: 12) {
                ProgressView()
                    .scaleEffect(0.8)
                    .progressViewStyle(CircularProgressViewStyle(tint: AppColor.youtubeRed.color))

                VStack(alignment: .leading, spacing: 2) {
                    Text("Generating Script...")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(AppColor.primary.color)

                    Text(scriptManager.currentStep)
                        .font(.system(size: 13, weight: .medium))
                        .foregroundColor(AppColor.grayText.color)
                }

                Spacer()

                Text("\(Int(scriptManager.generationProgress * 100))%")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.youtubeRed.color)
            }

            ProgressView(value: scriptManager.generationProgress)
                .progressViewStyle(LinearProgressViewStyle(tint: AppColor.youtubeRed.color))
                .scaleEffect(y: 1.5)
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(12)
    }

    // MARK: - Modern Generated Script Section
    private var modernGeneratedScriptSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Generated Script")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(AppColor.primary.color)

                Spacer()

                HStack(spacing: 6) {
                    if !scriptManager.generatedScript.isEmpty {
                        Button(action: {
                            scriptManager.copyScriptToClipboard()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "doc.on.clipboard")
                                    .font(.system(size: 12))
                                Text(scriptManager.isCopied ? "Copied!" : "Copy")
                                    .font(.system(size: 11, weight: .semibold))
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppColor.grayText.color.opacity(0.2))
                            .foregroundColor(AppColor.primary.color)
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)

                        Button(action: {
                            scriptManager.exportScript()
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.system(size: 12))
                                Text("Export")
                                    .font(.system(size: 11, weight: .semibold))
                            }
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(AppColor.youtubeRed.color)
                            .foregroundColor(.white)
                            .cornerRadius(6)
                        }
                        .buttonStyle(.plain)
                    }

                    Button(action: {
                        scriptManager.reset()
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "arrow.clockwise")
                                .font(.system(size: 12))
                            Text("New")
                                .font(.system(size: 11, weight: .semibold))
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(AppColor.grayText.color.opacity(0.2))
                        .foregroundColor(AppColor.primary.color)
                        .cornerRadius(6)
                    }
                    .buttonStyle(.plain)
                }
            }

            ScrollView {
                Markdown(scriptManager.generatedScript)
                    .font(.system(size: 14, weight: .regular))
                    .foregroundColor(AppColor.primary.color)
                    .textSelection(.enabled)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(12)
            }
            .frame(minHeight: 300, maxHeight: 500)
            .background(AppColor.darkGrayBackground.color)
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(AppColor.grayText.color.opacity(0.2), lineWidth: 1)
            )
        }
        .padding(16)
        .background(AppColor.darkGrayBackground.color.opacity(0.5))
        .cornerRadius(12)
    }

    // MARK: - Modern Generate Button Section
    private var modernGenerateButtonSection: some View {
        VStack(spacing: 16) {
            // Pre-generation Summary
            if !scriptManager.topic.isEmpty && !scriptManager.isGenerating {
                generationSummaryCard
            }

            // Generate Button
            Button(action: {
                scriptManager.generateScript()
            }) {
                HStack(spacing: 12) {
                    if scriptManager.isGenerating {
                        ProgressView()
                            .scaleEffect(0.9)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image(systemName: "wand.and.stars")
                            .font(.system(size: 18, weight: .bold))
                    }

                    VStack(spacing: 2) {
                        Text(scriptManager.isGenerating ? "Generating Your Script..." : "Generate Professional Script")
                            .font(.system(size: 16, weight: .bold))

                        if !scriptManager.isGenerating {
                            Text("AI-powered • Industry standard • Customized")
                                .font(.system(size: 12, weight: .medium))
                                .opacity(0.9)
                        }
                    }
                }
                .padding(.horizontal, 32)
                .padding(.vertical, 18)
                .frame(maxWidth: .infinity)
                .background(
                    LinearGradient(
                        colors: [AppColor.youtubeRed.color, AppColor.youtubeRed.color.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .foregroundColor(.white)
                .cornerRadius(12)
                .shadow(color: AppColor.youtubeRed.color.opacity(0.4), radius: 8, x: 0, y: 4)
            }
            .buttonStyle(.plain)
            .disabled(scriptManager.isGenerating || !isConfigurationComplete)
            .opacity(scriptManager.isGenerating || !isConfigurationComplete ? 0.6 : 1.0)

            // Generation Tips
            if !scriptManager.isGenerating && !isConfigurationComplete {
                HStack(spacing: 8) {
                    Image(systemName: "info.circle.fill")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(Color.orange)

                    Text("Please enter a video topic to generate your script")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(AppColor.textSecondary.color)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.orange.opacity(0.1))
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                        )
                )
            }
        }
        .padding(.horizontal, 24)
    }

    // MARK: - Generation Summary Card
    private var generationSummaryCard: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("Ready to Generate")
                    .font(.system(size: 14, weight: .bold))
                    .foregroundColor(AppColor.textPrimary.color)

                Spacer()

                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(Color.green)
            }

            VStack(alignment: .leading, spacing: 8) {
                summaryRow(title: "Type", value: scriptManager.selectedType.rawValue)
                summaryRow(title: "Length", value: scriptManager.selectedLength.rawValue)
                summaryRow(title: "Tone", value: scriptManager.selectedTone.rawValue)

                if !scriptManager.targetAudience.isEmpty {
                    summaryRow(title: "Audience", value: scriptManager.targetAudience)
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.green.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.green.opacity(0.2), lineWidth: 1)
                )
        )
    }

    private func summaryRow(title: String, value: String) -> some View {
        HStack {
            Text(title)
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(AppColor.textSecondary.color)

            Spacer()

            Text(value)
                .font(.system(size: 12, weight: .semibold))
                .foregroundColor(AppColor.textPrimary.color)
        }
    }
}

struct ScriptTypeSelector: View {
    @Binding var selectedType: ScriptType

    let columns = [
        GridItem(.adaptive(minimum: 140), spacing: 12)
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Video Type")
                .font(AppFontStyle.headline.style.weight(.semibold))
                .foregroundColor(AppColor.primary.color)

            LazyVGrid(columns: columns, spacing: 12) {
                ForEach(ScriptType.allCases, id: \.self) { type in
                    Button(action: {
                        selectedType = type
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type.icon)
                                .font(.system(size: 24))
                                .foregroundColor(selectedType == type ? .white : AppColor.youtubeRed.color)

                            Text(type.rawValue)
                                .font(AppFontStyle.caption1.style.weight(.semibold))
                                .foregroundColor(selectedType == type ? .white : AppColor.primary.color)
                                .multilineTextAlignment(.center)
                        }
                        .padding(12)
                        .frame(minHeight: 80)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(selectedType == type ? AppColor.youtubeRed.color : AppColor.darkGrayBackground.color)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(
                                    selectedType == type ? AppColor.youtubeRed.color : AppColor.grayText.color.opacity(0.2),
                                    lineWidth: selectedType == type ? 2 : 1
                                )
                        )
                    }
                    .buttonStyle(.plain)
                }
            }

            if selectedType != .educational {
                Text(selectedType.description)
                    .font(AppFontStyle.caption1.style)
                    .foregroundColor(AppColor.grayText.color)
                    .padding(.top, 4)
            }
        }
    }
}

#Preview {
    ScriptWriterView()
}
